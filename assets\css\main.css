@import "tailwindcss";
@import "@nuxt/ui";

@theme static {
  --font-sans: "Public Sans", sans-serif;

  --breakpoint-3xl: 1920px;

  --color-primary: #38bdf8;
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;
}

/* Custom base styles */
@layer base {
  html,
  body {
    font-family: sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #ffffff;
  }
}
/* Custom components */
@layer components {
  .btn-primary {
    background-color: rgb(59, 130, 246);
    color: white;
    font-weight: bold;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
  }
  .btn-primary:hover {
    background-color: rgb(29, 78, 216);
  }
}
/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
}
