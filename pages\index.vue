<script lang="ts" setup>
useHead({
  title: "Home - NuxtEvent",
  meta: [
    {
      name: "description",
      content: "Welcome to NuxtEvent - Professional Event Management Platform",
    },
  ],
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900">Welcome to NuxtEvent</h1>
        <p class="mt-2 text-sm text-gray-600">
          Professional Event Management Platform
        </p>
      </div>
      
      <div class="space-y-4">
        <NuxtLink
          to="/auth/login"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Go to Login
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
