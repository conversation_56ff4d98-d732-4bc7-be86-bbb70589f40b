// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  devtools: { enabled: true },
  modules: ["@nuxt/ui"],
  css: ["~/assets/css/main.css"],

  // Development server configuration for better hot reload
  vite: {
    server: {
      hmr: {
        port: 24678, // Use a specific port for HMR
      },
    },
    // Add better error handling for development
    define: {
      __DEV__: true,
    },
  },

  // SSR configuration to prevent unsafe attribute warnings
  ssr: true,

  // Development configuration
  dev: process.env.NODE_ENV !== "production",

  // Experimental features for better development experience
  experimental: {
    payloadExtraction: false, // Disable payload extraction for faster dev builds
  },

  // Component auto-import configuration
  components: [
    {
      path: "~/components",
      extensions: ["vue"],
      pathPrefix: false,
    },
  ],

  // App configuration
  app: {
    head: {
      // Basic meta tags
      charset: "utf-8",
      viewport: "width=device-width, initial-scale=1",

      // Default title and meta description
      title: "NuxtEvent - Professional Event Management Platform",
      meta: [
        {
          name: "description",
          content:
            "NuxtEvent is a comprehensive event management platform that helps you create, organize, and manage events seamlessly. From planning to execution, we've got you covered.",
        },
        {
          name: "keywords",
          content:
            "event management, event planning, event organization, conferences, workshops, seminars, event platform",
        },
        {
          name: "author",
          content: "NuxtEvent Team",
        },
        {
          name: "robots",
          content: "index, follow",
        },

        // Open Graph meta tags for social media sharing
        {
          property: "og:type",
          content: "website",
        },
        {
          property: "og:title",
          content: "NuxtEvent - Professional Event Management Platform",
        },
        {
          property: "og:description",
          content:
            "NuxtEvent is a comprehensive event management platform that helps you create, organize, and manage events seamlessly. From planning to execution, we've got you covered.",
        },
        {
          property: "og:image",
          content: "/og-image.jpg",
        },
        {
          property: "og:url",
          content: "https://eventhub.com",
        },
        {
          property: "og:site_name",
          content: "NuxtEvent",
        },

        // Twitter Card meta tags
        {
          name: "twitter:card",
          content: "summary_large_image",
        },
        {
          name: "twitter:title",
          content: "NuxtEvent - Professional Event Management Platform",
        },
        {
          name: "twitter:description",
          content:
            "NuxtEvent is a comprehensive event management platform that helps you create, organize, and manage events seamlessly.",
        },
        {
          name: "twitter:image",
          content: "/og-image.jpg",
        },

        // // Additional SEO meta tags
        // {
        //   name: "theme-color",
        //   content: "#3b82f6",
        // },
        // {
        //   name: "msapplication-TileColor",
        //   content: "#3b82f6",
        // },
        // {
        //   name: "application-name",
        //   content: "NuxtEvent",
        // },
      ],

      // Link tags for favicons and other resources
      link: [
        {
          rel: "icon",
          type: "image/x-icon",
          href: "/favicon.ico",
        },
        {
          rel: "apple-touch-icon",
          sizes: "180x180",
          href: "/apple-touch-icon.png",
        },
        {
          rel: "icon",
          type: "image/png",
          sizes: "32x32",
          href: "/favicon-32x32.png",
        },
        {
          rel: "icon",
          type: "image/png",
          sizes: "16x16",
          href: "/favicon-16x16.png",
        },
        {
          rel: "manifest",
          href: "/site.webmanifest",
        },
      ],
    },
  },

  // Runtime configuration for API base URL
  runtimeConfig: {
    // Private keys (only available on server-side)
    apiBaseUrl: process.env.API_BASE_URL || "http://localhost:3000/api",

    // Public keys (exposed to client-side)
    public: {
      apiBaseUrl: process.env.API_BASE_URL || "http://localhost:3000/api",
    },
  },
});
