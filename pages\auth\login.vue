<script lang="ts" setup>
import type { FormSubmitEvent } from "@nuxt/ui";
import z from "zod";

definePageMeta({
  layout: "auth",
});

useHead({
  title: "Login",
  meta: [
    {
      name: "description",
      content: "Login to your account",
    },
  ],
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});
type LoginSchema = z.infer<typeof loginSchema>;

const formState = reactive({
  email: "",
  password: "",
});
const onSubmit = (event: FormSubmitEvent<LoginSchema>) => {
  console.log(event.data);
};
</script>
<template>
  <UCard>
    <div class="bg-brand-400 text-primary mb-2 text-2xl font-bold">Login</div>
    <div class="mb-4 text-sm text-gray-500">
      Don't have an account?
      <span class="text-primary font-bold">Register here</span>
    </div>
    <UForm
      :schema="loginSchema"
      :state="formState"
      class="space-y-6"
      @submit="onSubmit"
    >
      <UFormField label="Email" name="email">
        <UInput v-model="formState.email" />
      </UFormField>

      <UFormField label="Password" name="password">
        <UInput v-model="formState.password" type="password" />
      </UFormField>

      <UButton type="submit"> Submit </UButton>
    </UForm>
  </UCard>
</template>
